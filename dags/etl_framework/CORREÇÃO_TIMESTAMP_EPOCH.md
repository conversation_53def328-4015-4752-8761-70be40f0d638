# Correção do Problema TIMESTAMP 'epoch' no Framework ETL

## 🐛 Problema Identificado

O framework ETL estava gerando erros SQL como:
```
Incorrect syntax near 'epoch'.DB-Lib error message 20018, severity 15:
General SQL Server error: Check messages from the SQL Server
```

## 🔍 Causa Raiz

O problema estava na diferença de sintaxe entre as DAGs V3 (que funcionam) e o novo framework:

- **DAGs V3 (corretas)**: `TIMESTAMP ''epoch''` (aspas duplas dentro do OPENQUERY)
- **Framework (incorreto)**: `TIMESTAMP 'epoch'` (aspas simples dentro do OPENQUERY)

Quando se usa OPENQUERY no SQL Server, a string interna precisa ter aspas duplas para caracteres literais PostgreSQL.

## ✅ Arquivos Corrigidos

### 1. `strategies/incremental_daily.py`
```python
# ANTES (incorreto)
return """(TIMESTAMP 'epoch' + dt_inc * INTERVAL '1 millisecond' >= DATE_TRUNC('day', CURRENT_TIMESTAMP)
          OR
          TIMESTAMP 'epoch' + dt_alt * INTERVAL '1 millisecond' >= DATE_TRUNC('day', CURRENT_TIMESTAMP))"""

# DEPOIS (correto)
return """(TIMESTAMP ''epoch'' + dt_inc * INTERVAL ''1 millisecond'' >= DATE_TRUNC(''day'', CURRENT_TIMESTAMP)
          OR
          TIMESTAMP ''epoch'' + dt_alt * INTERVAL ''1 millisecond'' >= DATE_TRUNC(''day'', CURRENT_TIMESTAMP))"""
```

### 2. `strategies/incremental_seven_days.py`
```python
# ANTES (incorreto)
return """(TIMESTAMP 'epoch' + dt_inc * INTERVAL '1 millisecond' >= CURRENT_TIMESTAMP - INTERVAL '7 days'
          OR
          TIMESTAMP 'epoch' + dt_alt * INTERVAL '1 millisecond' >= CURRENT_TIMESTAMP - INTERVAL '7 days')"""

# DEPOIS (correto)
return """(TIMESTAMP ''epoch'' + dt_inc * INTERVAL ''1 millisecond'' >= CURRENT_TIMESTAMP - INTERVAL ''7 days''
          OR
          TIMESTAMP ''epoch'' + dt_alt * INTERVAL ''1 millisecond'' >= CURRENT_TIMESTAMP - INTERVAL ''7 days'')"""
```

### 3. `config/filter_config.py`
Corrigidos os métodos:
- `_build_daily_filter()`
- `_build_seven_days_filter()`
- `_build_monthly_filter()`

### 4. `config/table_config.py`
Corrigidos os métodos:
- `_build_default_daily_filter()`
- `_build_default_seven_days_filter()`

## 🧪 Como Testar a Correção

1. **Teste Rápido**: Execute uma DAG que usa o framework com uma tabela pequena
2. **Verificar Logs**: Os logs não devem mais mostrar erros de sintaxe SQL
3. **Comparar com V3**: O comportamento deve ser idêntico às DAGs V3 originais

## 📋 Checklist de Verificação

- [x] Corrigir `incremental_daily.py`
- [x] Corrigir `incremental_seven_days.py`
- [x] Corrigir `filter_config.py`
- [x] Corrigir `table_config.py`
- [ ] Testar com DAG real
- [ ] Verificar logs de execução
- [ ] Confirmar que não há mais erros de sintaxe

## 🎯 Resultado Esperado

Após as correções, o framework deve:
1. Gerar queries SQL com sintaxe correta para OPENQUERY
2. Executar processamento incremental sem erros
3. Ter comportamento idêntico às DAGs V3 originais

## 📝 Notas Importantes

- Esta correção é **sistemática** e resolve o problema em todos os lugares onde ocorria
- A sintaxe correta para OPENQUERY é: `TIMESTAMP ''epoch''` (aspas duplas)
- O problema afetava apenas conexões SQL Server com OPENQUERY
- Conexões diretas PostgreSQL não eram afetadas
